<?php
session_start();
date_default_timezone_set("Asia/Kolkata");
require_once "config.php";
$link->query("SET time_zone = '+05:30'");

if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) { 
    header("location: login.php"); 
    exit; 
}
$user_id = $_SESSION["user_id"];

// Handle form submission for adding employee
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'add_employee') {
    try {
        // Start transaction
        $link->autocommit(FALSE);
        
        // Sanitize and validate input
        $employee_id = sanitize_input($_POST['employee_id']);
        $employee_name = sanitize_input($_POST['employee_name']);
        $dob = sanitize_input($_POST['dob']);
        $present_address = sanitize_input($_POST['present_address']);
        $permanent_address = sanitize_input($_POST['permanent_address']);
        $mobile_number = sanitize_input($_POST['mobile_number']);
        $blood_group = sanitize_input($_POST['blood_group']);
        $aadhar_number = sanitize_input($_POST['aadhar_number']);
        $pan_number = sanitize_input($_POST['pan_number']);
        $bank_name = sanitize_input($_POST['bank_name']);
        $account_number = sanitize_input($_POST['account_number']);
        $ifsc_code = sanitize_input($_POST['ifsc_code']);
        $branch = sanitize_input($_POST['branch']);
        $educational_qualification = sanitize_input($_POST['educational_qualification']);
        $marital_status = sanitize_input($_POST['marital_status']);
        $date_of_joining = sanitize_input($_POST['date_of_joining']);
        $previous_experience = sanitize_input($_POST['previous_experience']);
        $previous_esi_number = sanitize_input($_POST['previous_esi_number']);
        $previous_uan = sanitize_input($_POST['previous_uan']);
        $father_name = sanitize_input($_POST['father_name']);
        $father_aadhar = sanitize_input($_POST['father_aadhar']);
        $mother_name = sanitize_input($_POST['mother_name']);
        $mother_aadhar = sanitize_input($_POST['mother_aadhar']);
        
        // Check if employee ID already exists
        $check_query = "SELECT id FROM employees WHERE employee_id = ?";
        $check_stmt = $link->prepare($check_query);
        $check_stmt->bind_param("s", $employee_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            throw new Exception("Employee ID already exists!");
        }
        
        // Check if Aadhar number already exists
        $aadhar_check = "SELECT id FROM employees WHERE aadhar_number = ?";
        $aadhar_stmt = $link->prepare($aadhar_check);
        $aadhar_stmt->bind_param("s", $aadhar_number);
        $aadhar_stmt->execute();
        $aadhar_result = $aadhar_stmt->get_result();
        
        if ($aadhar_result->num_rows > 0) {
            throw new Exception("Aadhar number already exists!");
        }
        
        // Check if PAN number already exists
        $pan_check = "SELECT id FROM employees WHERE pan_number = ?";
        $pan_stmt = $link->prepare($pan_check);
        $pan_stmt->bind_param("s", $pan_number);
        $pan_stmt->execute();
        $pan_result = $pan_stmt->get_result();
        
        if ($pan_result->num_rows > 0) {
            throw new Exception("PAN number already exists!");
        }
        
        // Handle file uploads
        $photo = handle_file_upload($_FILES['photo']);
        $aadhar_copy = handle_file_upload($_FILES['aadhar_copy']);
        $pan_copy = handle_file_upload($_FILES['pan_copy']);
        $bank_copy = handle_file_upload($_FILES['bank_copy']);
        $education_certificates = handle_file_upload($_FILES['education_certificates']);
        $experience_copy = handle_file_upload($_FILES['experience_copy']);
        $father_aadhar_copy = handle_file_upload($_FILES['father_aadhar_copy']);
        $mother_aadhar_copy = handle_file_upload($_FILES['mother_aadhar_copy']);
        
        // Insert employee data
        $insert_query = "INSERT INTO employees (
            employee_id, employee_name, photo, dob, present_address, permanent_address, 
            mobile_number, aadhar_number, aadhar_copy, pan_number, pan_copy, 
            bank_name, account_number, ifsc_code, branch, blood_group, 
            educational_qualification, education_certificates, marital_status, 
            date_of_joining, previous_experience, experience_copy, 
            previous_esi_number, previous_uan, father_name, father_aadhar, 
            father_aadhar_copy, mother_name, mother_aadhar, mother_aadhar_copy
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )";
        
        $stmt = $link->prepare($insert_query);
        $stmt->bind_param("ssssssssssssssssssssssssssssss",
            $employee_id, $employee_name, $photo, $dob, $present_address, $permanent_address,
            $mobile_number, $aadhar_number, $aadhar_copy, $pan_number, $pan_copy,
            $bank_name, $account_number, $ifsc_code, $branch, $blood_group,
            $educational_qualification, $education_certificates, $marital_status,
            $date_of_joining, $previous_experience, $experience_copy,
            $previous_esi_number, $previous_uan, $father_name, $father_aadhar,
            $father_aadhar_copy, $mother_name, $mother_aadhar, $mother_aadhar_copy
        );
        
        if (!$stmt->execute()) {
            throw new Exception("Error inserting employee data: " . $stmt->error);
        }
        
        $new_employee_id = $link->insert_id;
        
        // Insert family members
        if (isset($_POST['family_member_name']) && is_array($_POST['family_member_name'])) {
            $family_names = $_POST['family_member_name'];
            $family_aadhars = $_POST['family_member_aadhar'];
            $family_relations = $_POST['family_member_relation'];
            
            $family_insert = "INSERT INTO family_members (employee_id, member_name, aadhar_number, relation, aadhar_copy) VALUES (?, ?, ?, ?, ?)";
            $family_stmt = $link->prepare($family_insert);
            
            for ($i = 0; $i < count($family_names); $i++) {
                if (!empty($family_names[$i])) {
                    $member_name = sanitize_input($family_names[$i]);
                    $member_aadhar = sanitize_input($family_aadhars[$i]);
                    $member_relation = sanitize_input($family_relations[$i]);
                    
                    // Handle family member aadhar copy upload
                    $member_aadhar_copy = null;
                    if (isset($_FILES['family_member_aadhar_copy']['name'][$i]) && 
                        !empty($_FILES['family_member_aadhar_copy']['name'][$i])) {
                        
                        $file_array = array(
                            'name' => $_FILES['family_member_aadhar_copy']['name'][$i],
                            'type' => $_FILES['family_member_aadhar_copy']['type'][$i],
                            'tmp_name' => $_FILES['family_member_aadhar_copy']['tmp_name'][$i],
                            'error' => $_FILES['family_member_aadhar_copy']['error'][$i],
                            'size' => $_FILES['family_member_aadhar_copy']['size'][$i]
                        );
                        $member_aadhar_copy = handle_file_upload($file_array);
                    }
                    
                    $family_stmt->bind_param("issss", $new_employee_id, $member_name, $member_aadhar, $member_relation, $member_aadhar_copy);
                    
                    if (!$family_stmt->execute()) {
                        throw new Exception("Error inserting family member data: " . $family_stmt->error);
                    }
                }
            }
        }
        
        // Commit transaction
        $link->commit();
        
        $success_message = "Employee added successfully!";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $link->rollback();
        $error_message = $e->getMessage();
        
        // Delete uploaded files on error
        if (isset($photo) && $photo) delete_file($photo);
        if (isset($aadhar_copy) && $aadhar_copy) delete_file($aadhar_copy);
        if (isset($pan_copy) && $pan_copy) delete_file($pan_copy);
        if (isset($bank_copy) && $bank_copy) delete_file($bank_copy);
        if (isset($education_certificates) && $education_certificates) delete_file($education_certificates);
        if (isset($experience_copy) && $experience_copy) delete_file($experience_copy);
        if (isset($father_aadhar_copy) && $father_aadhar_copy) delete_file($father_aadhar_copy);
        if (isset($mother_aadhar_copy) && $mother_aadhar_copy) delete_file($mother_aadhar_copy);
    } finally {
        $link->autocommit(TRUE);
    }
}

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $employee_id = intval($_GET['id']);
    
    // Get employee files for deletion
    $file_query = "SELECT photo, aadhar_copy, pan_copy, education_certificates, experience_copy, 
                   father_aadhar_copy, mother_aadhar_copy FROM employees WHERE id = ?";
    $file_stmt = $link->prepare($file_query);
    $file_stmt->bind_param("i", $employee_id);
    $file_stmt->execute();
    $file_result = $file_stmt->get_result();
    
    if ($file_row = $file_result->fetch_assoc()) {
        // Delete associated files
        foreach ($file_row as $file) {
            if ($file) {
                delete_file($file);
            }
        }
    }
    
    // Delete family members first (foreign key constraint)
    $delete_family = "DELETE FROM family_members WHERE employee_id = ?";
    $family_stmt = $link->prepare($delete_family);
    $family_stmt->bind_param("i", $employee_id);
    $family_stmt->execute();
    
    // Delete employee
    $delete_employee = "DELETE FROM employees WHERE id = ?";
    $emp_stmt = $link->prepare($delete_employee);
    $emp_stmt->bind_param("i", $employee_id);
    
    if ($emp_stmt->execute()) {
        header("location: employee_management.php?message=" . urlencode("Employee deleted successfully!") . "&type=success");
    } else {
        header("location: employee_management.php?message=" . urlencode("Error deleting employee!") . "&type=danger");
    }
    exit;
}

// Get search and filter parameters
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$department_filter = isset($_GET['department']) ? sanitize_input($_GET['department']) : '';
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : 'Active';

// Pagination
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Build WHERE clause
$where_conditions = ["1=1"];
$params = [];
$types = "";

if (!empty($search)) {
    $where_conditions[] = "(employee_name LIKE ? OR employee_id LIKE ? OR mobile_number LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $types .= "sss";
}

if (!empty($department_filter)) {
    $where_conditions[] = "educational_qualification LIKE ?";
    $params[] = "%$department_filter%";
    $types .= "s";
}

$where_clause = implode(" AND ", $where_conditions);

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM employees WHERE $where_clause";
$count_stmt = $link->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$count_result = $count_stmt->get_result();
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $records_per_page);

// Get employees with pagination
$query = "SELECT id, employee_id, employee_name, mobile_number, educational_qualification, 
          date_of_joining, photo, created_at FROM employees 
          WHERE $where_clause 
          ORDER BY created_at DESC 
          LIMIT ? OFFSET ?";

$stmt = $link->prepare($query);
$limit_params = array_merge($params, [$records_per_page, $offset]);
$limit_types = $types . "ii";
$stmt->bind_param($limit_types, ...$limit_params);
$stmt->execute();
$result = $stmt->get_result();

// Get statistics
$stats_query = "SELECT 
    COUNT(*) as total_employees,
    COUNT(*) as active_employees,
    COUNT(CASE WHEN MONTH(date_of_joining) = MONTH(CURRENT_DATE()) 
         AND YEAR(date_of_joining) = YEAR(CURRENT_DATE()) THEN 1 END) as new_this_month,
    COUNT(DISTINCT educational_qualification) as total_departments
    FROM employees";
$stats_result = $link->query($stats_query);
$stats = $stats_result->fetch_assoc();

$page_title = "Complete Employee Management System";
require_once "header.php";
?>

<!-- Custom Styles for this page -->
<style>
.nav-tabs .nav-link {
    color: #6c757d;
    border: none;
    border-bottom: 3px solid transparent;
    background: none;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    border-bottom-color: #0d6efd;
    background: none;
}

.nav-tabs .nav-link:hover {
    color: #0d6efd;
    border-bottom-color: rgba(13, 110, 253, 0.3);
}

.employee-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.action-buttons .btn {
    margin-right: 0.25rem;
}

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem 1.25rem;
    margin: -1px -1px 0 -1px;
    border-radius: 0.375rem 0.375rem 0 0;
}

.section-header h6 {
    margin: 0;
    font-weight: 600;
}

.required::after {
    content: " *";
    color: #dc3545;
}

.file-upload-info {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.family-member-row {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
}

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-down {
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

.hover-glow:hover {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
    transition: text-shadow 0.2s ease;
}
</style>

<div class="container mt-4">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="mb-0">
                    <i class="las la-users me-2 text-primary"></i>Complete Employee Management
                </h2>
                <p class="text-muted mb-0">Manage all employee information in one place</p>
            </div>
            <div class="col-auto">
                <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                    <i class="las la-sync me-2"></i>Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="las la-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="las la-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs mb-4" id="managementTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="employee-list-tab" data-bs-toggle="tab" data-bs-target="#employee-list" type="button" role="tab">
                <i class="las la-list me-2"></i>Employee Directory
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="add-employee-tab" data-bs-toggle="tab" data-bs-target="#add-employee" type="button" role="tab">
                <i class="las la-user-plus me-2"></i>Add New Employee
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="managementTabsContent">
        <!-- Employee List Tab -->
        <div class="tab-pane fade show active" id="employee-list" role="tabpanel">
            <!-- Employee Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Employees</h6>
                                    <h3 class="mb-0"><?php echo $stats['total_employees']; ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="las la-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active</h6>
                                    <h3 class="mb-0"><?php echo $stats['active_employees']; ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="las la-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">New This Month</h6>
                                    <h3 class="mb-0"><?php echo $stats['new_this_month']; ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="las la-user-plus fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Departments</h6>
                                    <h3 class="mb-0"><?php echo $stats['total_departments']; ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="las la-building fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Search Employees</label>
                                    <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by name, ID, or mobile...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <select class="form-control" name="department">
                                        <option value="">All Departments</option>
                                        <option value="IT" <?php echo $department_filter == 'IT' ? 'selected' : ''; ?>>IT</option>
                                        <option value="HR" <?php echo $department_filter == 'HR' ? 'selected' : ''; ?>>HR</option>
                                        <option value="Finance" <?php echo $department_filter == 'Finance' ? 'selected' : ''; ?>>Finance</option>
                                        <option value="Operations" <?php echo $department_filter == 'Operations' ? 'selected' : ''; ?>>Operations</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Status</label>
                                    <select class="form-control" name="status">
                                        <option value="">All Status</option>
                                        <option value="Active" <?php echo $status_filter == 'Active' ? 'selected' : ''; ?>>Active</option>
                                        <option value="Inactive" <?php echo $status_filter == 'Inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-outline-primary w-100">
                                    <i class="las la-filter me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Employee List -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="las la-list me-2"></i>Employee List
                            </h5>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-sm btn-primary" onclick="document.getElementById('add-employee-tab').click()">
                                <i class="las la-plus me-1"></i>Add Employee
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="60">Photo</th>
                                    <th>Employee ID</th>
                                    <th>Name</th>
                                    <th>Mobile</th>
                                    <th>Department</th>
                                    <th>Join Date</th>
                                    <th>Status</th>
                                    <th width="160">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($result->num_rows > 0): ?>
                                    <?php while ($employee = $result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <?php if ($employee['photo']): ?>
                                                    <img src="uploads/<?php echo htmlspecialchars($employee['photo']); ?>"
                                                         class="employee-photo" alt="Photo">
                                                <?php else: ?>
                                                    <div class="employee-photo bg-secondary d-flex align-items-center justify-content-center text-white">
                                                        <?php echo substr($employee['employee_name'], 0, 1); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td><strong><?php echo htmlspecialchars($employee['employee_id']); ?></strong></td>
                                            <td><?php echo htmlspecialchars($employee['employee_name']); ?></td>
                                            <td><?php echo htmlspecialchars($employee['mobile_number']); ?></td>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    <?php echo htmlspecialchars($employee['educational_qualification'] ?: 'N/A'); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('d/m/Y', strtotime($employee['date_of_joining'])); ?></td>
                                            <td><span class="status-badge bg-success text-white">Active</span></td>
                                            <td class="action-buttons">
                                                <a href="view_employee.php?id=<?php echo $employee['id']; ?>"
                                                   class="btn btn-sm btn-outline-info" title="View Details">
                                                    <i class="las la-eye"></i>
                                                </a>
                                                <a href="edit_employee.php?id=<?php echo $employee['id']; ?>"
                                                   class="btn btn-sm btn-outline-warning" title="Edit">
                                                    <i class="las la-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDelete(<?php echo $employee['id']; ?>, '<?php echo htmlspecialchars($employee['employee_name']); ?>')"
                                                        title="Delete">
                                                    <i class="las la-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="las la-users fa-3x mb-3"></i>
                                                <p class="mb-0">No employees found</p>
                                                <?php if (!empty($search) || !empty($department_filter)): ?>
                                                    <small>Try adjusting your search criteria</small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing <?php echo ($offset + 1); ?> to <?php echo min($offset + $records_per_page, $total_records); ?>
                        of <?php echo $total_records; ?> entries
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <!-- Previous Page -->
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page-1; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                    Previous
                                </a>
                            </li>

                            <!-- Page Numbers -->
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <!-- Next Page -->
                            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page+1; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                    Next
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        </div>

        <!-- Add Employee Tab -->
        <div class="tab-pane fade" id="add-employee" role="tabpanel">
            <form id="employeeForm" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="add_employee">

                <!-- Basic Information -->
                <div class="card mb-4">
                    <div class="section-header">
                        <h6><i class="las la-user me-2"></i>Basic Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">Employee ID</label>
                                <input type="text" class="form-control" name="employee_id" required value="<?php echo isset($_POST['employee_id']) ? htmlspecialchars($_POST['employee_id']) : ''; ?>">
                                <div class="form-text">Must be unique (e.g., EMP001)</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">Employee Name</label>
                                <input type="text" class="form-control" name="employee_name" required value="<?php echo isset($_POST['employee_name']) ? htmlspecialchars($_POST['employee_name']) : ''; ?>">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Photo</label>
                                <input type="file" class="form-control" name="photo" accept="image/*">
                                <div class="file-upload-info">Supported formats: JPG, PNG, GIF (Max: 5MB)</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">Date of Birth</label>
                                <input type="date" class="form-control" name="dob" required value="<?php echo isset($_POST['dob']) ? $_POST['dob'] : ''; ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card mb-4">
                    <div class="section-header">
                        <h6><i class="las la-address-book me-2"></i>Contact Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">Present Address</label>
                                <textarea class="form-control" name="present_address" rows="3" required><?php echo isset($_POST['present_address']) ? htmlspecialchars($_POST['present_address']) : ''; ?></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">Permanent Address</label>
                                <textarea class="form-control" name="permanent_address" rows="3" required><?php echo isset($_POST['permanent_address']) ? htmlspecialchars($_POST['permanent_address']) : ''; ?></textarea>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">Mobile Number</label>
                                <input type="tel" class="form-control" name="mobile_number" pattern="[0-9]{10}" required value="<?php echo isset($_POST['mobile_number']) ? $_POST['mobile_number'] : ''; ?>">
                                <div class="form-text">10-digit mobile number</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Blood Group</label>
                                <select class="form-control" name="blood_group">
                                    <option value="">Select Blood Group</option>
                                    <option value="A+" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'A+') ? 'selected' : ''; ?>>A+</option>
                                    <option value="A-" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'A-') ? 'selected' : ''; ?>>A-</option>
                                    <option value="B+" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'B+') ? 'selected' : ''; ?>>B+</option>
                                    <option value="B-" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'B-') ? 'selected' : ''; ?>>B-</option>
                                    <option value="AB+" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'AB+') ? 'selected' : ''; ?>>AB+</option>
                                    <option value="AB-" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'AB-') ? 'selected' : ''; ?>>AB-</option>
                                    <option value="O+" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'O+') ? 'selected' : ''; ?>>O+</option>
                                    <option value="O-" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'O-') ? 'selected' : ''; ?>>O-</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Identity Documents -->
                <div class="card mb-4">
                    <div class="section-header">
                        <h6><i class="las la-id-card me-2"></i>Identity Documents</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">Aadhar Number</label>
                                <input type="text" class="form-control" name="aadhar_number" pattern="[0-9]{12}" maxlength="12" required value="<?php echo isset($_POST['aadhar_number']) ? $_POST['aadhar_number'] : ''; ?>">
                                <div class="form-text">12-digit Aadhar number (must be unique)</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Aadhar Copy</label>
                                <input type="file" class="form-control" name="aadhar_copy" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="file-upload-info">Upload PDF, JPG, or PNG file</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">PAN Number</label>
                                <input type="text" class="form-control" name="pan_number" pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}" maxlength="10" style="text-transform: uppercase;" required value="<?php echo isset($_POST['pan_number']) ? $_POST['pan_number'] : ''; ?>">
                                <div class="form-text">Format: ********** (must be unique)</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">PAN Copy</label>
                                <input type="file" class="form-control" name="pan_copy" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="file-upload-info">Upload PDF, JPG, or PNG file</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bank Details -->
                <div class="card mb-4">
                    <div class="section-header">
                        <h6><i class="las la-university me-2"></i>Bank Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">Bank Name</label>
                                <input type="text" class="form-control" name="bank_name" required value="<?php echo isset($_POST['bank_name']) ? htmlspecialchars($_POST['bank_name']) : ''; ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">Account Number</label>
                                <input type="text" class="form-control" name="account_number" required value="<?php echo isset($_POST['account_number']) ? $_POST['account_number'] : ''; ?>">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">IFSC Code</label>
                                <input type="text" class="form-control" name="ifsc_code" style="text-transform: uppercase;" required value="<?php echo isset($_POST['ifsc_code']) ? $_POST['ifsc_code'] : ''; ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">Branch</label>
                                <input type="text" class="form-control" name="branch" required value="<?php echo isset($_POST['branch']) ? htmlspecialchars($_POST['branch']) : ''; ?>">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Bank Documents</label>
                                <input type="file" class="form-control" name="bank_copy" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="file-upload-info">Upload passbook or cheque copy (PDF, JPG, PNG)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employment Details -->
                <div class="card mb-4">
                    <div class="section-header">
                        <h6><i class="las la-briefcase me-2"></i>Employment Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Educational Qualification</label>
                                <textarea class="form-control" name="educational_qualification" rows="2"><?php echo isset($_POST['educational_qualification']) ? htmlspecialchars($_POST['educational_qualification']) : ''; ?></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Education Certificates</label>
                                <input type="file" class="form-control" name="education_certificates" accept=".pdf,.jpg,.jpeg,.png" multiple>
                                <div class="file-upload-info">Upload multiple files if needed</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Marital Status</label>
                                <select class="form-control" name="marital_status">
                                    <option value="Single" <?php echo (isset($_POST['marital_status']) && $_POST['marital_status'] == 'Single') ? 'selected' : ''; ?>>Single</option>
                                    <option value="Married" <?php echo (isset($_POST['marital_status']) && $_POST['marital_status'] == 'Married') ? 'selected' : ''; ?>>Married</option>
                                    <option value="Divorced" <?php echo (isset($_POST['marital_status']) && $_POST['marital_status'] == 'Divorced') ? 'selected' : ''; ?>>Divorced</option>
                                    <option value="Widowed" <?php echo (isset($_POST['marital_status']) && $_POST['marital_status'] == 'Widowed') ? 'selected' : ''; ?>>Widowed</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">Date of Joining</label>
                                <input type="date" class="form-control" name="date_of_joining" required value="<?php echo isset($_POST['date_of_joining']) ? $_POST['date_of_joining'] : date('Y-m-d'); ?>">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Previous Experience</label>
                                <textarea class="form-control" name="previous_experience" rows="2" placeholder="Describe previous work experience"><?php echo isset($_POST['previous_experience']) ? htmlspecialchars($_POST['previous_experience']) : ''; ?></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Experience Copy</label>
                                <input type="file" class="form-control" name="experience_copy" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="file-upload-info">Upload experience certificate</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Previous ESI Number</label>
                                <input type="text" class="form-control" name="previous_esi_number" placeholder="If applicable" value="<?php echo isset($_POST['previous_esi_number']) ? $_POST['previous_esi_number'] : ''; ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Previous UAN</label>
                                <input type="text" class="form-control" name="previous_uan" placeholder="If applicable" value="<?php echo isset($_POST['previous_uan']) ? $_POST['previous_uan'] : ''; ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Parent Details -->
                <div class="card mb-4">
                    <div class="section-header">
                        <h6><i class="las la-users me-2"></i>Parent Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">Father's Name</label>
                                <input type="text" class="form-control" name="father_name" required value="<?php echo isset($_POST['father_name']) ? htmlspecialchars($_POST['father_name']) : ''; ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">Father's Aadhar</label>
                                <input type="text" class="form-control" name="father_aadhar" pattern="[0-9]{12}" maxlength="12" required value="<?php echo isset($_POST['father_aadhar']) ? $_POST['father_aadhar'] : ''; ?>">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">Mother's Name</label>
                                <input type="text" class="form-control" name="mother_name" required value="<?php echo isset($_POST['mother_name']) ? htmlspecialchars($_POST['mother_name']) : ''; ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">Mother's Aadhar</label>
                                <input type="text" class="form-control" name="mother_aadhar" pattern="[0-9]{12}" maxlength="12" required value="<?php echo isset($_POST['mother_aadhar']) ? $_POST['mother_aadhar'] : ''; ?>">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Father's Aadhar Copy</label>
                                <input type="file" class="form-control" name="father_aadhar_copy" accept=".pdf,.jpg,.jpeg,.png">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Mother's Aadhar Copy</label>
                                <input type="file" class="form-control" name="mother_aadhar_copy" accept=".pdf,.jpg,.jpeg,.png">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other Family Members -->
                <div class="card mb-4">
                    <div class="section-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="las la-users me-2"></i>Other Family Members</h6>
                            <button type="button" class="btn btn-sm btn-outline-light" onclick="addFamilyMember()">
                                <i class="las la-plus me-1"></i>Add Member
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="familyMembersContainer">
                            <!-- Family members will be added here -->
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <button type="button" class="btn btn-secondary me-2" onclick="document.getElementById('employee-list-tab').click()">
                                    <i class="las la-times me-2"></i>Cancel
                                </button>
                                <button type="reset" class="btn btn-outline-warning me-2">
                                    <i class="las la-redo me-2"></i>Reset Form
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="las la-save me-2"></i>Save Employee
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="employeeName"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="las la-exclamation-triangle me-2"></i>
                    This action cannot be undone. All related data including family members and documents will be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="las la-trash me-2"></i>Delete Employee
                </a>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
    let familyMemberCount = 0;

    function addFamilyMember() {
        familyMemberCount++;
        const container = document.getElementById('familyMembersContainer');
        const memberDiv = document.createElement('div');
        memberDiv.className = 'family-member-row';
        memberDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Family Member ${familyMemberCount}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFamilyMember(this)">
                    <i class="las la-times"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Name</label>
                    <input type="text" class="form-control" name="family_member_name[]">
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Aadhar Number</label>
                    <input type="text" class="form-control" name="family_member_aadhar[]" pattern="[0-9]{12}" maxlength="12">
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Relation</label>
                    <select class="form-control" name="family_member_relation[]">
                        <option value="">Select Relation</option>
                        <option value="Spouse">Spouse</option>
                        <option value="Child">Child</option>
                        <option value="Sibling">Sibling</option>
                        <option value="Parent">Parent</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label">Aadhar Copy</label>
                    <input type="file" class="form-control" name="family_member_aadhar_copy[]" accept=".pdf,.jpg,.jpeg,.png">
                </div>
            </div>
        `;
        container.appendChild(memberDiv);
    }

    function removeFamilyMember(button) {
        button.closest('.family-member-row').remove();
    }

    function confirmDelete(employeeId, employeeName) {
        document.getElementById('employeeName').textContent = employeeName;
        document.getElementById('confirmDeleteBtn').href = `employee_management.php?action=delete&id=${employeeId}`;

        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }

    // Show success/error messages from URL parameters
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        const type = urlParams.get('type') || 'success';

        if (message) {
            showAlert(decodeURIComponent(message), type);
            // Clean the URL
            const newUrl = window.location.pathname + '?' +
                          new URLSearchParams(Array.from(urlParams.entries()).filter(([key]) =>
                              !['message', 'type'].includes(key))).toString();
            window.history.replaceState({}, document.title, newUrl);
        }

        // Form validation and input formatting
        // Format Aadhar numbers
        const aadharInputs = document.querySelectorAll('input[pattern="[0-9]{12}"]');
        aadharInputs.forEach(input => {
            input.addEventListener('input', function(e) {
                e.target.value = e.target.value.replace(/\D/g, '').substring(0, 12);
            });
        });

        // Format mobile number
        const mobileInput = document.querySelector('input[name="mobile_number"]');
        if (mobileInput) {
            mobileInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.replace(/\D/g, '').substring(0, 10);
            });
        }

        // Format PAN number
        const panInput = document.querySelector('input[name="pan_number"]');
        if (panInput) {
            panInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10);
            });
        }

        // Format IFSC code
        const ifscInput = document.querySelector('input[name="ifsc_code"]');
        if (ifscInput) {
            ifscInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 11);
            });
        }

        // Form submission validation
        const form = document.getElementById('employeeForm');
        if (form) {
            form.addEventListener('submit', function(e) {
                // Additional validation can be added here
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                } else {
                    // Show loading state
                    const submitBtn = form.querySelector('button[type="submit"]');
                    submitBtn.innerHTML = '<i class="las la-spinner la-spin me-2"></i>Saving...';
                    submitBtn.disabled = true;
                }
                form.classList.add('was-validated');
            });
        }

        // Switch to add employee tab if there was an error in form submission
        <?php if (isset($error_message)): ?>
            document.getElementById('add-employee-tab').click();
        <?php endif; ?>
    });

    // Dynamic family member aadhar formatting
    document.addEventListener('input', function(e) {
        if (e.target.name === 'family_member_aadhar[]') {
            e.target.value = e.target.value.replace(/\D/g, '').substring(0, 12);
        }
    });

    function showAlert(message, type = 'success') {
        const alertContainer = document.getElementById('alertContainer');
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="las la-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
</script>

</body>
</html>
