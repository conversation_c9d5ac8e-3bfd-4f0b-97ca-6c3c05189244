 <?php
// Initialize the session
session_start();
 
// Check if the user is logged in, if not then redirect him to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true){
    header("location: login.php");
    exit;
}

require_once "config.php";

// Insert record
if (isset($_POST['add_expense'])) {
    $description = $_POST['description'];
    $category = $_POST['category'];
    $amount = $_POST['amount'];

    $sql = "INSERT INTO expense_tracker (description, category, amount) VALUES ('$description', '$category', '$amount')";
    $link->query($sql);
    header("Location: index.php");
}

// Delete record
if (isset($_GET['delete'])) {
    $id = $_GET['delete'];
    $link->query("DELETE FROM expense_tracker WHERE id=$id");
    header("Location: index.php");
}

// Fetch records
$result = $link->query("SELECT * FROM expense_tracker ORDER BY created_at DESC");

require_once"header.php";

?>
    <div class="container mt-5 text-center">
        <h1 class="my-5">Hi, <b><?php echo htmlspecialchars($_SESSION["username"]); ?></b>. Welcome to our site.</h1>
        <p>
            <a href="reset-password.php" class="btn btn-warning">Reset Your Password</a>
            <a href="logout.php" class="btn btn-danger ml-3">Sign Out of Your Account</a>
        </p>
    </div>
    <div class="container mt-4">
    <h2 class="mb-4">Expense Tracker</h2>

    <!-- Add Expense Form -->
    <div class="card p-3 mb-4">
        <form action="" method="POST">
            <div class="mb-3">
                <label class="form-label">Description</label>
                <input type="text" name="description" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Category</label>
                <input type="text" name="category" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Amount</label>
                <input type="number" step="0.01" name="amount" class="form-control" required>
            </div>
            <button type="submit" name="add_expense" class="btn btn-primary">Add Expense</button>
        </form>
    </div>

    <!-- Expenses Table -->
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Description</th>
                <th>Category</th>
                <th>Amount</th>
                <th>Created At</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?= $row['id']; ?></td>
                    <td><?= $row['description']; ?></td>
                    <td><?= $row['category']; ?></td>
                    <td>$<?= number_format($row['amount'], 2); ?></td>
                    <td><?= $row['created_at']; ?></td>
                    <td>
                        <a href="edit.php?id=<?= $row['id']; ?>" class="btn btn-warning btn-sm">Edit</a>
                        <a href="index.php?delete=<?= $row['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?');">Delete</a>
                    </td>
                </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</div>
</body>
</html