/* Minimal CSS for Employee Management System */

/* Global Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Hanken Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #2c3e50;
    line-height: 1.6;
}

/* Navbar */
.navbar {
    background: linear-gradient(45deg, #007bff, #0056b3);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 700;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    background: white;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Forms */
.form-control {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 0.75rem;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.required::after {
    content: " *";
    color: #dc3545;
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}

/* Table */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Page Header */
.page-header {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Section Header */
.section-header {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border-left: 4px solid #007bff;
}

.section-header h6 {
    margin: 0;
    color: #007bff;
    font-weight: 600;
}

/* Family Member Rows */
.family-member-row {
    background-color: #f8f9fa;
    margin: 10px 0;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

/* Employee Photo */
.employee-photo {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
}

/* Status Badge */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

/* Action Buttons */
.action-buttons .btn {
    margin-right: 5px;
    padding: 5px 10px;
    font-size: 12px;
}

/* File Upload Info */
.file-upload-info {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 8px;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .page-header {
        padding: 1rem;
    }
    
    .action-buttons .btn {
        margin: 2px;
        padding: 4px 8px;
    }
}