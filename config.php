<?php
// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$database = "ctq_hr";

try {
    // Create connection using MySQLi
    $link = new mysqli($servername, $username, $password, $database);
    
    // Check connection
    if ($link->connect_error) {
        die("Connection failed: " . $link->connect_error);
    }
    
    // Set charset to utf8
    $link->set_charset("utf8");
    
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Function to sanitize input
function sanitize_input($data) {
    global $link;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $link->real_escape_string($data);
}

// Function to handle file uploads
function handle_file_upload($file, $upload_dir = 'uploads/') {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return null;
    }
    
    // Create upload directory if it doesn't exist
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $file_extension;
    $target_path = $upload_dir . $filename;
    
    // Validate file type
    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
    if (!in_array(strtolower($file_extension), $allowed_types)) {
        return false;
    }
    
    // Check file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        return false;
    }
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $target_path)) {
        return $filename;
    }
    
    return false;
}

// Function to delete file
function delete_file($filename, $upload_dir = 'uploads/') {
    if ($filename && file_exists($upload_dir . $filename)) {
        unlink($upload_dir . $filename);
        return true;
    }
    return false;
}
?>