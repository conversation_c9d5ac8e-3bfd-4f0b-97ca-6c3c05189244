<?php
session_start();
date_default_timezone_set("Asia/Kolkata");
require_once "config.php";
$link->query("SET time_zone = '+05:30'");

if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) { 
    header("location: login.php"); 
    exit; 
}
$user_id = $_SESSION["user_id"];

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // Start transaction
        $link->autocommit(FALSE);
        
        // Sanitize and validate input
        $employee_id = sanitize_input($_POST['employee_id']);
        $employee_name = sanitize_input($_POST['employee_name']);
        $dob = sanitize_input($_POST['dob']);
        $present_address = sanitize_input($_POST['present_address']);
        $permanent_address = sanitize_input($_POST['permanent_address']);
        $mobile_number = sanitize_input($_POST['mobile_number']);
        $blood_group = sanitize_input($_POST['blood_group']);
        $aadhar_number = sanitize_input($_POST['aadhar_number']);
        $pan_number = sanitize_input($_POST['pan_number']);
        $bank_name = sanitize_input($_POST['bank_name']);
        $account_number = sanitize_input($_POST['account_number']);
        $ifsc_code = sanitize_input($_POST['ifsc_code']);
        $branch = sanitize_input($_POST['branch']);
        $educational_qualification = sanitize_input($_POST['educational_qualification']);
        $marital_status = sanitize_input($_POST['marital_status']);
        $date_of_joining = sanitize_input($_POST['date_of_joining']);
        $previous_experience = sanitize_input($_POST['previous_experience']);
        $previous_esi_number = sanitize_input($_POST['previous_esi_number']);
        $previous_uan = sanitize_input($_POST['previous_uan']);
        $father_name = sanitize_input($_POST['father_name']);
        $father_aadhar = sanitize_input($_POST['father_aadhar']);
        $mother_name = sanitize_input($_POST['mother_name']);
        $mother_aadhar = sanitize_input($_POST['mother_aadhar']);
        
        // Check if employee ID already exists
        $check_query = "SELECT id FROM employees WHERE employee_id = ?";
        $check_stmt = $link->prepare($check_query);
        $check_stmt->bind_param("s", $employee_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            throw new Exception("Employee ID already exists!");
        }
        
        // Check if Aadhar number already exists
        $aadhar_check = "SELECT id FROM employees WHERE aadhar_number = ?";
        $aadhar_stmt = $link->prepare($aadhar_check);
        $aadhar_stmt->bind_param("s", $aadhar_number);
        $aadhar_stmt->execute();
        $aadhar_result = $aadhar_stmt->get_result();
        
        if ($aadhar_result->num_rows > 0) {
            throw new Exception("Aadhar number already exists!");
        }
        
        // Check if PAN number already exists
        $pan_check = "SELECT id FROM employees WHERE pan_number = ?";
        $pan_stmt = $link->prepare($pan_check);
        $pan_stmt->bind_param("s", $pan_number);
        $pan_stmt->execute();
        $pan_result = $pan_stmt->get_result();
        
        if ($pan_result->num_rows > 0) {
            throw new Exception("PAN number already exists!");
        }
        
        // Handle file uploads
        $photo = handle_file_upload($_FILES['photo']);
        $aadhar_copy = handle_file_upload($_FILES['aadhar_copy']);
        $pan_copy = handle_file_upload($_FILES['pan_copy']);
        $bank_copy = handle_file_upload($_FILES['bank_copy']);
        $education_certificates = handle_file_upload($_FILES['education_certificates']);
        $experience_copy = handle_file_upload($_FILES['experience_copy']);
        $father_aadhar_copy = handle_file_upload($_FILES['father_aadhar_copy']);
        $mother_aadhar_copy = handle_file_upload($_FILES['mother_aadhar_copy']);
        
        // Insert employee data
        $insert_query = "INSERT INTO employees (
            employee_id, employee_name, photo, dob, present_address, permanent_address, 
            mobile_number, aadhar_number, aadhar_copy, pan_number, pan_copy, 
            bank_name, account_number, ifsc_code, branch, blood_group, 
            educational_qualification, education_certificates, marital_status, 
            date_of_joining, previous_experience, experience_copy, 
            previous_esi_number, previous_uan, father_name, father_aadhar, 
            father_aadhar_copy, mother_name, mother_aadhar, mother_aadhar_copy
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )";
        
        $stmt = $link->prepare($insert_query);
        $stmt->bind_param("ssssssssssssssssssssssssssssss",
            $employee_id, $employee_name, $photo, $dob, $present_address, $permanent_address,
            $mobile_number, $aadhar_number, $aadhar_copy, $pan_number, $pan_copy,
            $bank_name, $account_number, $ifsc_code, $branch, $blood_group,
            $educational_qualification, $education_certificates, $marital_status,
            $date_of_joining, $previous_experience, $experience_copy,
            $previous_esi_number, $previous_uan, $father_name, $father_aadhar,
            $father_aadhar_copy, $mother_name, $mother_aadhar, $mother_aadhar_copy
        );
        
        if (!$stmt->execute()) {
            throw new Exception("Error inserting employee data: " . $stmt->error);
        }
        
        $new_employee_id = $link->insert_id;
        
        // Insert family members
        if (isset($_POST['family_member_name']) && is_array($_POST['family_member_name'])) {
            $family_names = $_POST['family_member_name'];
            $family_aadhars = $_POST['family_member_aadhar'];
            $family_relations = $_POST['family_member_relation'];
            
            $family_insert = "INSERT INTO family_members (employee_id, member_name, aadhar_number, relation, aadhar_copy) VALUES (?, ?, ?, ?, ?)";
            $family_stmt = $link->prepare($family_insert);
            
            for ($i = 0; $i < count($family_names); $i++) {
                if (!empty($family_names[$i])) {
                    $member_name = sanitize_input($family_names[$i]);
                    $member_aadhar = sanitize_input($family_aadhars[$i]);
                    $member_relation = sanitize_input($family_relations[$i]);
                    
                    // Handle family member aadhar copy upload
                    $member_aadhar_copy = null;
                    if (isset($_FILES['family_member_aadhar_copy']['name'][$i]) && 
                        !empty($_FILES['family_member_aadhar_copy']['name'][$i])) {
                        
                        $file_array = array(
                            'name' => $_FILES['family_member_aadhar_copy']['name'][$i],
                            'type' => $_FILES['family_member_aadhar_copy']['type'][$i],
                            'tmp_name' => $_FILES['family_member_aadhar_copy']['tmp_name'][$i],
                            'error' => $_FILES['family_member_aadhar_copy']['error'][$i],
                            'size' => $_FILES['family_member_aadhar_copy']['size'][$i]
                        );
                        $member_aadhar_copy = handle_file_upload($file_array);
                    }
                    
                    $family_stmt->bind_param("issss", $new_employee_id, $member_name, $member_aadhar, $member_relation, $member_aadhar_copy);
                    
                    if (!$family_stmt->execute()) {
                        throw new Exception("Error inserting family member data: " . $family_stmt->error);
                    }
                }
            }
        }
        
        // Commit transaction
        $link->commit();
        
        header("location: index.php?message=" . urlencode("Employee added successfully!") . "&type=success");
        exit;
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $link->rollback();
        $error_message = $e->getMessage();
        
        // Delete uploaded files on error
        if (isset($photo) && $photo) delete_file($photo);
        if (isset($aadhar_copy) && $aadhar_copy) delete_file($aadhar_copy);
        if (isset($pan_copy) && $pan_copy) delete_file($pan_copy);
        if (isset($bank_copy) && $bank_copy) delete_file($bank_copy);
        if (isset($education_certificates) && $education_certificates) delete_file($education_certificates);
        if (isset($experience_copy) && $experience_copy) delete_file($experience_copy);
        if (isset($father_aadhar_copy) && $father_aadhar_copy) delete_file($father_aadhar_copy);
        if (isset($mother_aadhar_copy) && $mother_aadhar_copy) delete_file($mother_aadhar_copy);
    } finally {
        $link->autocommit(TRUE);
    }
}

$page_title = "Add Employee - Employee Management System";
require_once "header.php";
?>

<?php if (isset($error_message)): ?>
    <div class="container mt-4">
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="las la-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
<?php endif; ?>

<div class="container mt-4">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="mb-0">
                    <i class="las la-user-plus me-2 text-primary"></i>Add New Employee
                </h2>
                <p class="text-muted mb-0">Fill in the employee details below</p>
            </div>
            <div class="col-auto">
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="las la-arrow-left me-2"></i>Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Employee Form -->
    <form id="employeeForm" method="POST" enctype="multipart/form-data">
        <!-- Basic Information -->
        <div class="card mb-4">
            <div class="section-header">
                <h6><i class="las la-user me-2"></i>Basic Information</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label required">Employee ID</label>
                        <input type="text" class="form-control" name="employee_id" required value="<?php echo isset($_POST['employee_id']) ? htmlspecialchars($_POST['employee_id']) : ''; ?>">
                        <div class="form-text">Must be unique (e.g., EMP001)</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Employee Name</label>
                        <input type="text" class="form-control" name="employee_name" required value="<?php echo isset($_POST['employee_name']) ? htmlspecialchars($_POST['employee_name']) : ''; ?>">
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Photo</label>
                        <input type="file" class="form-control" name="photo" accept="image/*">
                        <div class="file-upload-info">Supported formats: JPG, PNG, GIF (Max: 5MB)</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Date of Birth</label>
                        <input type="date" class="form-control" name="dob" required value="<?php echo isset($_POST['dob']) ? $_POST['dob'] : ''; ?>">
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card mb-4">
            <div class="section-header">
                <h6><i class="las la-address-book me-2"></i>Contact Information</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label required">Present Address</label>
                        <textarea class="form-control" name="present_address" rows="3" required><?php echo isset($_POST['present_address']) ? htmlspecialchars($_POST['present_address']) : ''; ?></textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Permanent Address</label>
                        <textarea class="form-control" name="permanent_address" rows="3" required><?php echo isset($_POST['permanent_address']) ? htmlspecialchars($_POST['permanent_address']) : ''; ?></textarea>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label required">Mobile Number</label>
                        <input type="tel" class="form-control" name="mobile_number" pattern="[0-9]{10}" required value="<?php echo isset($_POST['mobile_number']) ? $_POST['mobile_number'] : ''; ?>">
                        <div class="form-text">10-digit mobile number</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Blood Group</label>
                        <select class="form-control" name="blood_group">
                            <option value="">Select Blood Group</option>
                            <option value="A+" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'A+') ? 'selected' : ''; ?>>A+</option>
                            <option value="A-" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'A-') ? 'selected' : ''; ?>>A-</option>
                            <option value="B+" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'B+') ? 'selected' : ''; ?>>B+</option>
                            <option value="B-" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'B-') ? 'selected' : ''; ?>>B-</option>
                            <option value="AB+" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'AB+') ? 'selected' : ''; ?>>AB+</option>
                            <option value="AB-" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'AB-') ? 'selected' : ''; ?>>AB-</option>
                            <option value="O+" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'O+') ? 'selected' : ''; ?>>O+</option>
                            <option value="O-" <?php echo (isset($_POST['blood_group']) && $_POST['blood_group'] == 'O-') ? 'selected' : ''; ?>>O-</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Identity Documents -->
        <div class="card mb-4">
            <div class="section-header">
                <h6><i class="las la-id-card me-2"></i>Identity Documents</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label required">Aadhar Number</label>
                        <input type="text" class="form-control" name="aadhar_number" pattern="[0-9]{12}" maxlength="12" required value="<?php echo isset($_POST['aadhar_number']) ? $_POST['aadhar_number'] : ''; ?>">
                        <div class="form-text">12-digit Aadhar number (must be unique)</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Aadhar Copy</label>
                        <input type="file" class="form-control" name="aadhar_copy" accept=".pdf,.jpg,.jpeg,.png">
                        <div class="file-upload-info">Upload PDF, JPG, or PNG file</div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label required">PAN Number</label>
                        <input type="text" class="form-control" name="pan_number" pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}" maxlength="10" style="text-transform: uppercase;" required value="<?php echo isset($_POST['pan_number']) ? $_POST['pan_number'] : ''; ?>">
                        <div class="form-text">Format: ********** (must be unique)</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">PAN Copy</label>
                        <input type="file" class="form-control" name="pan_copy" accept=".pdf,.jpg,.jpeg,.png">
                        <div class="file-upload-info">Upload PDF, JPG, or PNG file</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bank Details -->
        <div class="card mb-4">
            <div class="section-header">
                <h6><i class="las la-university me-2"></i>Bank Details</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label required">Bank Name</label>
                        <input type="text" class="form-control" name="bank_name" required value="<?php echo isset($_POST['bank_name']) ? htmlspecialchars($_POST['bank_name']) : ''; ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Account Number</label>
                        <input type="text" class="form-control" name="account_number" required value="<?php echo isset($_POST['account_number']) ? $_POST['account_number'] : ''; ?>">
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label required">IFSC Code</label>
                        <input type="text" class="form-control" name="ifsc_code" style="text-transform: uppercase;" required value="<?php echo isset($_POST['ifsc_code']) ? $_POST['ifsc_code'] : ''; ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Branch</label>
                        <input type="text" class="form-control" name="branch" required value="<?php echo isset($_POST['branch']) ? htmlspecialchars($_POST['branch']) : ''; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <label class="form-label">Bank Documents</label>
                        <input type="file" class="form-control" name="bank_copy" accept=".pdf,.jpg,.jpeg,.png">
                        <div class="file-upload-info">Upload passbook or cheque copy (PDF, JPG, PNG)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employment Details -->
        <div class="card mb-4">
            <div class="section-header">
                <h6><i class="las la-briefcase me-2"></i>Employment Details</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Educational Qualification</label>
                        <textarea class="form-control" name="educational_qualification" rows="2"><?php echo isset($_POST['educational_qualification']) ? htmlspecialchars($_POST['educational_qualification']) : ''; ?></textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Education Certificates</label>
                        <input type="file" class="form-control" name="education_certificates" accept=".pdf,.jpg,.jpeg,.png" multiple>
                        <div class="file-upload-info">Upload multiple files if needed</div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Marital Status</label>
                        <select class="form-control" name="marital_status">
                            <option value="Single" <?php echo (isset($_POST['marital_status']) && $_POST['marital_status'] == 'Single') ? 'selected' : ''; ?>>Single</option>
                            <option value="Married" <?php echo (isset($_POST['marital_status']) && $_POST['marital_status'] == 'Married') ? 'selected' : ''; ?>>Married</option>
                            <option value="Divorced" <?php echo (isset($_POST['marital_status']) && $_POST['marital_status'] == 'Divorced') ? 'selected' : ''; ?>>Divorced</option>
                            <option value="Widowed" <?php echo (isset($_POST['marital_status']) && $_POST['marital_status'] == 'Widowed') ? 'selected' : ''; ?>>Widowed</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Date of Joining</label>
                        <input type="date" class="form-control" name="date_of_joining" required value="<?php echo isset($_POST['date_of_joining']) ? $_POST['date_of_joining'] : date('Y-m-d'); ?>">
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Previous Experience</label>
                        <textarea class="form-control" name="previous_experience" rows="2" placeholder="Describe previous work experience"><?php echo isset($_POST['previous_experience']) ? htmlspecialchars($_POST['previous_experience']) : ''; ?></textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Experience Copy</label>
                        <input type="file" class="form-control" name="experience_copy" accept=".pdf,.jpg,.jpeg,.png">
                        <div class="file-upload-info">Upload experience certificate</div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Previous ESI Number</label>
                        <input type="text" class="form-control" name="previous_esi_number" placeholder="If applicable" value="<?php echo isset($_POST['previous_esi_number']) ? $_POST['previous_esi_number'] : ''; ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Previous UAN</label>
                        <input type="text" class="form-control" name="previous_uan" placeholder="If applicable" value="<?php echo isset($_POST['previous_uan']) ? $_POST['previous_uan'] : ''; ?>">
                    </div>
                </div>
            </div>
        </div>

        <!-- Parent Details -->
        <div class="card mb-4">
            <div class="section-header">
                <h6><i class="las la-users me-2"></i>Parent Details</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label required">Father's Name</label>
                        <input type="text" class="form-control" name="father_name" required value="<?php echo isset($_POST['father_name']) ? htmlspecialchars($_POST['father_name']) : ''; ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Father's Aadhar</label>
                        <input type="text" class="form-control" name="father_aadhar" pattern="[0-9]{12}" maxlength="12" required value="<?php echo isset($_POST['father_aadhar']) ? $_POST['father_aadhar'] : ''; ?>">
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label required">Mother's Name</label>
                        <input type="text" class="form-control" name="mother_name" required value="<?php echo isset($_POST['mother_name']) ? htmlspecialchars($_POST['mother_name']) : ''; ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Mother's Aadhar</label>
                        <input type="text" class="form-control" name="mother_aadhar" pattern="[0-9]{12}" maxlength="12" required value="<?php echo isset($_POST['mother_aadhar']) ? $_POST['mother_aadhar'] : ''; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Father's Aadhar Copy</label>
                        <input type="file" class="form-control" name="father_aadhar_copy" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Mother's Aadhar Copy</label>
                        <input type="file" class="form-control" name="mother_aadhar_copy" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                </div>
            </div>
        </div>

        <!-- Other Family Members -->
        <div class="card mb-4">
            <div class="section-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="las la-users me-2"></i>Other Family Members</h6>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addFamilyMember()">
                        <i class="las la-plus me-1"></i>Add Member
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="familyMembersContainer">
                    <!-- Family members will be added here -->
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12 text-center">
                        <button type="button" class="btn btn-secondary me-2" onclick="window.history.back()">
                            <i class="las la-times me-2"></i>Cancel
                        </button>
                        <button type="reset" class="btn btn-outline-warning me-2">
                            <i class="las la-redo me-2"></i>Reset Form
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="las la-save me-2"></i>Save Employee
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
    let familyMemberCount = 0;

    function addFamilyMember() {
        familyMemberCount++;
        const container = document.getElementById('familyMembersContainer');
        const memberDiv = document.createElement('div');
        memberDiv.className = 'family-member-row';
        memberDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Family Member ${familyMemberCount}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFamilyMember(this)">
                    <i class="las la-times"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Name</label>
                    <input type="text" class="form-control" name="family_member_name[]">
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Aadhar Number</label>
                    <input type="text" class="form-control" name="family_member_aadhar[]" pattern="[0-9]{12}" maxlength="12">
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Relation</label>
                    <select class="form-control" name="family_member_relation[]">
                        <option value="">Select Relation</option>
                        <option value="Spouse">Spouse</option>
                        <option value="Child">Child</option>
                        <option value="Sibling">Sibling</option>
                        <option value="Parent">Parent</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label">Aadhar Copy</label>
                    <input type="file" class="form-control" name="family_member_aadhar_copy[]" accept=".pdf,.jpg,.jpeg,.png">
                </div>
            </div>
        `;
        container.appendChild(memberDiv);
    }

    function removeFamilyMember(button) {
        button.closest('.family-member-row').remove();
    }

    // Form validation and input formatting
    document.addEventListener('DOMContentLoaded', function() {
        // Format Aadhar numbers
        const aadharInputs = document.querySelectorAll('input[pattern="[0-9]{12}"]');
        aadharInputs.forEach(input => {
            input.addEventListener('input', function(e) {
                e.target.value = e.target.value.replace(/\D/g, '').substring(0, 12);
            });
        });

        // Format mobile number
        const mobileInput = document.querySelector('input[name="mobile_number"]');
        if (mobileInput) {
            mobileInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.replace(/\D/g, '').substring(0, 10);
            });
        }

        // Format PAN number
        const panInput = document.querySelector('input[name="pan_number"]');
        if (panInput) {
            panInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10);
            });
        }

        // Format IFSC code
        const ifscInput = document.querySelector('input[name="ifsc_code"]');
        if (ifscInput) {
            ifscInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 11);
            });
        }

        // Form submission validation
        const form = document.getElementById('employeeForm');
        form.addEventListener('submit', function(e) {
            // Additional validation can be added here
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="las la-spinner la-spin me-2"></i>Saving...';
                submitBtn.disabled = true;
            }
            form.classList.add('was-validated');
        });
    });

    // Dynamic family member aadhar formatting
    document.addEventListener('input', function(e) {
        if (e.target.name === 'family_member_aadhar[]') {
            e.target.value = e.target.value.replace(/\D/g, '').substring(0, 12);
        }
    });
</script>

</body>
</html>