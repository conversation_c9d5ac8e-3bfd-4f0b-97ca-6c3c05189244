/* Animation Styles for Employee Management System */

/* Base Keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-100px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(100px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3) translateY(-30px);
    }
    50% {
        opacity: 1;
        transform: scale(1.05) translateY(-10px);
    }
    70% {
        transform: scale(0.98) translateY(0);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.6), 0 0 30px rgba(102, 126, 234, 0.4);
    }
    100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
    }
}

@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes flipIn {
    from {
        opacity: 0;
        transform: rotateY(-90deg);
    }
    to {
        opacity: 1;
        transform: rotateY(0deg);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

.slide-down {
    animation: slideDown 0.6s ease-out forwards;
}

.slide-up {
    animation: slideUp 0.6s ease-out forwards;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out forwards;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out forwards;
}

.scale-in {
    animation: scaleIn 0.5s ease-out forwards;
}

.bounce-in {
    animation: bounceIn 0.8s ease-out forwards;
}

.zoom-in {
    animation: zoomIn 0.5s ease-out forwards;
}

.flip-in {
    animation: flipIn 0.6s ease-out forwards;
}

.slide-in-down {
    animation: slideInDown 0.5s ease-out forwards;
}

/* Hover Animations */
.hover-scale {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    animation: glow 2s infinite;
}

.hover-float {
    transition: transform 0.3s ease;
}

.hover-float:hover {
    animation: float 2s ease-in-out infinite;
}

.hover-shake:hover {
    animation: shake 0.5s ease-in-out;
}

.hover-pulse:hover {
    animation: pulse 1s ease-in-out infinite;
}

.hover-rotate:hover {
    transform: rotate(360deg);
    transition: transform 0.5s ease;
}

/* Loading Animations */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 1000px 100%;
    animation: shimmer 2s infinite linear;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: rotate 1s linear infinite;
    margin: 20px auto;
}

.loading-dots::after {
    content: '';
    animation: dots 2s infinite;
}

@keyframes dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* Delayed Animation Classes */
.delay-100 {
    animation-delay: 0.1s;
}

.delay-200 {
    animation-delay: 0.2s;
}

.delay-300 {
    animation-delay: 0.3s;
}