<?php
session_start();
date_default_timezone_set("Asia/Kolkata");
require_once "config.php";
$link->query("SET time_zone = '+05:30'");

if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) { 
    header("location: login.php"); 
    exit; 
}
$user_id = $_SESSION["user_id"];

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $employee_id = intval($_GET['id']);
    
    // Get employee files for deletion
    $file_query = "SELECT photo, aadhar_copy, pan_copy, education_certificates, experience_copy, 
                   father_aadhar_copy, mother_aadhar_copy FROM employees WHERE id = ?";
    $file_stmt = $link->prepare($file_query);
    $file_stmt->bind_param("i", $employee_id);
    $file_stmt->execute();
    $file_result = $file_stmt->get_result();
    
    if ($file_row = $file_result->fetch_assoc()) {
        // Delete associated files
        foreach ($file_row as $file) {
            if ($file) {
                delete_file($file);
            }
        }
    }
    
    // Delete family members first (foreign key constraint)
    $delete_family = "DELETE FROM family_members WHERE employee_id = ?";
    $family_stmt = $link->prepare($delete_family);
    $family_stmt->bind_param("i", $employee_id);
    $family_stmt->execute();
    
    // Delete employee
    $delete_employee = "DELETE FROM employees WHERE id = ?";
    $emp_stmt = $link->prepare($delete_employee);
    $emp_stmt->bind_param("i", $employee_id);
    
    if ($emp_stmt->execute()) {
        header("location: index.php?message=" . urlencode("Employee deleted successfully!") . "&type=success");
    } else {
        header("location: index.php?message=" . urlencode("Error deleting employee!") . "&type=danger");
    }
    exit;
}

// Get search and filter parameters
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$department_filter = isset($_GET['department']) ? sanitize_input($_GET['department']) : '';
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : 'Active';

// Pagination
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Build WHERE clause
$where_conditions = ["1=1"];
$params = [];
$types = "";

if (!empty($search)) {
    $where_conditions[] = "(employee_name LIKE ? OR employee_id LIKE ? OR mobile_number LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $types .= "sss";
}

if (!empty($department_filter)) {
    $where_conditions[] = "educational_qualification LIKE ?";
    $params[] = "%$department_filter%";
    $types .= "s";
}

if (!empty($status_filter)) {
    // For this example, we'll consider all employees as active
    // You can add a status column to the employees table if needed
}

$where_clause = implode(" AND ", $where_conditions);

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM employees WHERE $where_clause";
$count_stmt = $link->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$count_result = $count_stmt->get_result();
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $records_per_page);

// Get employees with pagination
$query = "SELECT id, employee_id, employee_name, mobile_number, educational_qualification, 
          date_of_joining, photo, created_at FROM employees 
          WHERE $where_clause 
          ORDER BY created_at DESC 
          LIMIT ? OFFSET ?";

$stmt = $link->prepare($query);
$limit_params = array_merge($params, [$records_per_page, $offset]);
$limit_types = $types . "ii";
$stmt->bind_param($limit_types, ...$limit_params);
$stmt->execute();
$result = $stmt->get_result();

// Get statistics
$stats_query = "SELECT 
    COUNT(*) as total_employees,
    COUNT(*) as active_employees,
    COUNT(CASE WHEN MONTH(date_of_joining) = MONTH(CURRENT_DATE()) 
         AND YEAR(date_of_joining) = YEAR(CURRENT_DATE()) THEN 1 END) as new_this_month,
    COUNT(DISTINCT educational_qualification) as total_departments
    FROM employees";
$stats_result = $link->query($stats_query);
$stats = $stats_result->fetch_assoc();

$page_title = "Employee Management System";
require_once "header.php";
?>

<div class="container mt-4">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="mb-0">
                    <i class="las la-list me-2 text-primary"></i>Employee Directory
                </h2>
                <p class="text-muted mb-0">Manage your organization's employee information</p>
            </div>
            <div class="col-auto">
                <a href="add_employee.php" class="btn btn-primary">
                    <i class="las la-plus me-2"></i>Add New Employee
                </a>
                <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                    <i class="las la-sync me-2"></i>Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- Employee Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Employees</h6>
                            <h3 class="mb-0"><?php echo $stats['total_employees']; ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="las la-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Active</h6>
                            <h3 class="mb-0"><?php echo $stats['active_employees']; ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="las la-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">New This Month</h6>
                            <h3 class="mb-0"><?php echo $stats['new_this_month']; ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="las la-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Departments</h6>
                            <h3 class="mb-0"><?php echo $stats['total_departments']; ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="las la-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Search Employees</label>
                            <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by name, ID, or mobile...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Department</label>
                            <select class="form-control" name="department">
                                <option value="">All Departments</option>
                                <option value="IT" <?php echo $department_filter == 'IT' ? 'selected' : ''; ?>>IT</option>
                                <option value="HR" <?php echo $department_filter == 'HR' ? 'selected' : ''; ?>>HR</option>
                                <option value="Finance" <?php echo $department_filter == 'Finance' ? 'selected' : ''; ?>>Finance</option>
                                <option value="Operations" <?php echo $department_filter == 'Operations' ? 'selected' : ''; ?>>Operations</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Status</label>
                            <select class="form-control" name="status">
                                <option value="">All Status</option>
                                <option value="Active" <?php echo $status_filter == 'Active' ? 'selected' : ''; ?>>Active</option>
                                <option value="Inactive" <?php echo $status_filter == 'Inactive' ? 'selected' : ''; ?>>Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="las la-filter me-1"></i>Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Employee List -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="las la-list me-2"></i>Employee List
                    </h5>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="60">Photo</th>
                            <th>Employee ID</th>
                            <th>Name</th>
                            <th>Mobile</th>
                            <th>Department</th>
                            <th>Join Date</th>
                            <th>Status</th>
                            <th width="160">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($employee = $result->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <?php if ($employee['photo']): ?>
                                            <img src="uploads/<?php echo htmlspecialchars($employee['photo']); ?>" 
                                                 class="employee-photo" alt="Photo">
                                        <?php else: ?>
                                            <div class="employee-photo bg-secondary d-flex align-items-center justify-content-center text-white">
                                                <?php echo substr($employee['employee_name'], 0, 1); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><strong><?php echo htmlspecialchars($employee['employee_id']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($employee['employee_name']); ?></td>
                                    <td><?php echo htmlspecialchars($employee['mobile_number']); ?></td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            <?php echo htmlspecialchars($employee['educational_qualification'] ?: 'N/A'); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('d/m/Y', strtotime($employee['date_of_joining'])); ?></td>
                                    <td><span class="status-badge bg-success text-white">Active</span></td>
                                    <td class="action-buttons">
                                        <a href="view_employee.php?id=<?php echo $employee['id']; ?>" 
                                           class="btn btn-sm btn-outline-info" title="View Details">
                                            <i class="las la-eye"></i>
                                        </a>
                                        <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" 
                                           class="btn btn-sm btn-outline-warning" title="Edit">
                                            <i class="las la-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete(<?php echo $employee['id']; ?>, '<?php echo htmlspecialchars($employee['employee_name']); ?>')" 
                                                title="Delete">
                                            <i class="las la-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="las la-users fa-3x mb-3"></i>
                                        <p class="mb-0">No employees found</p>
                                        <?php if (!empty($search) || !empty($department_filter)): ?>
                                            <small>Try adjusting your search criteria</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
                Showing <?php echo ($offset + 1); ?> to <?php echo min($offset + $records_per_page, $total_records); ?> 
                of <?php echo $total_records; ?> entries
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0">
                    <!-- Previous Page -->
                    <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $page-1; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                            Previous
                        </a>
                    </li>
                    
                    <!-- Page Numbers -->
                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);
                    
                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <!-- Next Page -->
                    <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $page+1; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                            Next
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="employeeName"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="las la-exclamation-triangle me-2"></i>
                    This action cannot be undone. All related data including family members and documents will be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="las la-trash me-2"></i>Delete Employee
                </a>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
    function confirmDelete(employeeId, employeeName) {
        document.getElementById('employeeName').textContent = employeeName;
        document.getElementById('confirmDeleteBtn').href = `index.php?action=delete&id=${employeeId}`;
        
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }

    // Show success/error messages from URL parameters
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        const type = urlParams.get('type') || 'success';
        
        if (message) {
            showAlert(decodeURIComponent(message), type);
            // Clean the URL
            const newUrl = window.location.pathname + '?' + 
                          new URLSearchParams(Array.from(urlParams.entries()).filter(([key]) => 
                              !['message', 'type'].includes(key))).toString();
            window.history.replaceState({}, document.title, newUrl);
        }
    });

    function showAlert(message, type = 'success') {
        const alertContainer = document.getElementById('alertContainer');
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="las la-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
</script>

</body>
</html>